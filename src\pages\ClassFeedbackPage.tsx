import React, { useState, useEffect } from 'react';
import { classService, ClassType } from '../services/classService';
import { Student, studentService } from '../services/student.service';
import { studentFeedbackService } from '../services/studentFeedbackService';
import { idCardService } from '../services/idCardService'; // Import idCardService instead of parentFeedbackService
import { supabase } from '../lib/api-client'; // Import supabase for auth
// TODO: Import UI components as needed

// Use ClassType from service
interface Class extends ClassType {}

// Define a student interface that matches what parentFeedbackService returns
interface StudentWithPhoto {
  id: string;
  name: string;
  photo_url: string | null;
}

interface FeedbackEntry {
  studentId: string;
  feedbackText: string;
}

const ClassFeedbackPage: React.FC = () => {
  const [selectedClass, setSelectedClass] = useState<ClassType | null>(null);
  const [classes, setClasses] = useState<ClassType[]>([]);
  const [students, setStudents] = useState<StudentWithPhoto[]>([]);
  const [feedbackEntries, setFeedbackEntries] = useState<FeedbackEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClasses = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const fetchedClasses = await classService.findMany(); // Use findMany
        setClasses(fetchedClasses);
      } catch (err) {
        console.error('Error fetching classes:', err);
        setError('Failed to fetch classes. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchClasses();
  }, []);

  // TODO: Fetch students when a class is selected
  useEffect(() => {
    const fetchStudents = async () => {
      if (!selectedClass) {
        console.log('No class selected, clearing students');
        setStudents([]);
        setFeedbackEntries([]);
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        console.log(`Fetching students for class ${selectedClass.id}...`);
        
        // First try to get students directly from the database using studentService.findMany
        try {
          console.log('Attempting to fetch real students using studentService.findMany...');
          const realStudents = await studentService.findMany({ classId: selectedClass.id });
          console.log('Students from studentService.findMany:', realStudents);
          
          if (realStudents && realStudents.length > 0) {
            // Check if these are real students or mock data
            const isMockData =false
            
            if (!isMockData) {
              console.log('Using real students from studentService.findMany');
              const formattedStudents = realStudents.map((student: Student) => ({
                id: student.id,
                name: student.name,
                photo_url: null // No photos from studentService.findMany
              }));
              setStudents(formattedStudents);
              setFeedbackEntries(
                formattedStudents.map((student: StudentWithPhoto) => ({ studentId: student.id, feedbackText: '' }))
              );
              return;
            } else {
              console.log('Detected mock data from studentService.findMany, trying idCardService...');
            }
          }
        } catch (err) {
          console.error('Error fetching students with studentService.findMany:', err);
        }
        
        // Fall back to idCardService if studentService.findMany fails or returns mock data
        console.log('Falling back to idCardService.getStudentsByClass...');
        const fetchedStudents = await idCardService.getStudentsByClass(selectedClass.id);
        console.log('Raw fetched students from idCardService:', fetchedStudents);
        
        // Check if these are mock data
        const isMockData = fetchedStudents.some(student =>
          student.student_name?.includes('Test Student') || student.id?.startsWith('STU00')
        );
        
        if (isMockData) {
          console.warn('WARNING: Still getting mock data from idCardService');
        }
        
        // Map the result to match our StudentWithPhoto interface
        const formattedStudents = fetchedStudents.map(student => ({
          id: student.id,
          name: student.student_name,
          photo_url: student.student_photo_url
        }));
        
        console.log('Formatted students:', formattedStudents);
        setStudents(formattedStudents);
        // Initialize feedback entries for the fetched students
        setFeedbackEntries(
          fetchedStudents.map(student => ({ studentId: student.id, feedbackText: '' }))
        );
      } catch (err) {
        console.error(`Error fetching students for class ${selectedClass.id}:`, err);
        setError('Failed to fetch students. Please try again.');
        setStudents([]);
        setFeedbackEntries([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStudents();
  }, [selectedClass]); // Removed students from dependency array, feedback re-init is handled within this effect

  const handleClassChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const classId = event.target.value;
    console.log(`Class selected: ${classId}`);
    const newSelectedClass = classes.find(c => c.id === classId) || null;
    console.log('Selected class object:', newSelectedClass);
    setSelectedClass(newSelectedClass);
  };

  const handleFeedbackChange = (studentId: string, feedbackText: string) => {
    setFeedbackEntries(prevEntries =>
      prevEntries.map(entry =>
        entry.studentId === studentId ? { ...entry, feedbackText } : entry
      )
    );
  };

  const handleSubmitFeedback = async () => {
    if (!selectedClass || feedbackEntries.length === 0) {
      setError('Please select a class and ensure there are students to provide feedback for.');
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const { data: sessionData } = await supabase.auth.getSession();
      if (!sessionData.session?.user) {
        setError('Could not get user session. Please log in and try again.');
        setIsLoading(false);
        return;
      }
      const userId = sessionData.session.user.id;

      const entriesToSubmit = feedbackEntries
        .filter(entry => entry.feedbackText.trim() !== '') // Only submit non-empty feedback
        .map(entry => {
          const student = students.find(s => s.id === entry.studentId);
          return {
            studentId: entry.studentId,
            studentName: student?.name || 'Unknown Student', // Get student name
            feedbackText: entry.feedbackText,
          };
        });

      if (entriesToSubmit.length === 0) {
        setError('No feedback entered to submit.');
        setIsLoading(false);
        return;
      }

      console.log('Submitting feedback:', {
        classId: selectedClass.id,
        feedbacks: entriesToSubmit,
        userId,
      });

      await studentFeedbackService.createBulkStudentFeedback(
        selectedClass.id,
        entriesToSubmit,
        userId
      );

      alert('Feedback submitted successfully!');
      // Optionally, clear form or redirect
      // setSelectedClass(null); // Example: Reset class selection
      // setFeedbackEntries([]);
    } catch (err) {
      console.error('Error submitting feedback:', err);
      setError('Failed to submit feedback. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Class Feedback</h1>

      {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <strong className="font-bold">Error:</strong>
        <span className="block sm:inline"> {error}</span>
      </div>}

      <div className="mb-4">
        <label htmlFor="class-select" className="block text-sm font-medium text-gray-700 mb-1">
          Select Class:
        </label>
        <select
          id="class-select"
          value={selectedClass?.id || ''}
          onChange={handleClassChange}
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="" disabled>-- Select a Class --</option>
          {classes.map(cls => (
            <option key={cls.id} value={cls.id}>
              {cls.name}
            </option>
          ))}
        </select>
      </div>

      {selectedClass && students.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-2">Students in {selectedClass.name}</h2>
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Student Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Feedback
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {students.map(student => {
                console.log('Rendering student:', student);
                const feedbackEntry = feedbackEntries.find(fe => fe.studentId === student.id);
                return (
                  <tr key={student.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 flex items-center gap-2">
                      {student.photo_url && (
                        <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 bg-gray-100">
                          <img
                            src={student.photo_url}
                            alt={student.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // If image fails to load, replace with placeholder
                              e.currentTarget.onerror = null;
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.parentElement!.innerHTML = `
                                <div class="w-8 h-8 rounded-full flex items-center justify-center bg-gray-200">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                  </svg>
                                </div>
                              `;
                            }}
                            crossOrigin="anonymous"
                          />
                        </div>
                      )}
                      {!student.photo_url && (
                        <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 bg-gray-200 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                          </svg>
                        </div>
                      )}
                      {student.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <textarea
                        value={feedbackEntry?.feedbackText || ''}
                        onChange={e => handleFeedbackChange(student.id, e.target.value)}
                        rows={2}
                        className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md p-2"
                        placeholder={`Enter feedback for ${student.name}`}
                      />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <button
            onClick={handleSubmitFeedback}
            disabled={isLoading}
            className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? 'Submitting...' : 'Submit All Feedback'}
          </button>
        </div>
      )}
      {selectedClass && students.length === 0 && !isLoading && (
        <p>No students found for this class, or students are still loading.</p>
      )}
    </div>
  );
};

export default ClassFeedbackPage;